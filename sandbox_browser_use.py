import os
import asyncio
import sys
import logging
from fastapi import <PERSON><PERSON><PERSON>, Request, Form, WebSocket, WebSocketDisconnect, BackgroundTasks, Depends, Response, Cookie
from fastapi.responses import HTMLResponse, RedirectResponse
from typing import List, Dict, Optional
import json
from datetime import datetime
from e2b_desktop import Sandbox

# Import functions from sandbox_desktop.py
#from sandbox_desktop import open_desktop_stream, setup_environment, create_sts
from sandbox_desktop import open_desktop_stream, setup_environment, create_sts

# Import session management
from user_session_manager import session_manager, get_current_user_session
from user_connection_manager import UserConnectionManager

# Global variables that will be shared with app.py (legacy - will be phased out)
desktop = None
stream_url = None
current_command = None

# These will be initialized from app.py
manager = None
logger = None
ws_handler = None
stdout_capture = None
stderr_capture = None
sessions = {}

# Session-aware connection manager
user_connection_manager = UserConnectionManager()

# WebSocket logger class for handling command output (session-aware)
class WebSocketLogger:
    def __init__(self, session_id, log_type="stdout"):
        self.session_id = session_id
        self.log_type = log_type
        self.loop = None

    def __call__(self, data):
        try:
            # Get or create event loop
            try:
                self.loop = asyncio.get_event_loop()
            except RuntimeError:
                self.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop)

            # Convert bytes to string if needed
            if isinstance(data, bytes):
                data = data.decode('utf-8', errors='replace')

            # Clean up the data
            data = data.rstrip()
            if not data:  # Skip empty lines
                return

            # Get timestamp
            timestamp = datetime.now().strftime("%H:%M:%S")

            # Create log entry
            log_entry = {
                "type": self.log_type,
                "timestamp": timestamp,
                "data": data
            }

            # Send to specific user session via WebSocket
            asyncio.run_coroutine_threadsafe(
                user_connection_manager.send_json_to_user(self.session_id, log_entry),
                self.loop
            )
        except Exception as e:
            # Don't use logging here to avoid potential infinite recursion
            print(f"Error in WebSocketLogger for session {self.session_id}: {e}", file=sys.stderr)

# WebSocket endpoint for real-time communication
async def websocket_endpoint(websocket: WebSocket, session_token: Optional[str] = Cookie(None)):
    logger.info(f"WebSocket connection attempt with session_token: {session_token}")
    logger.info(f"Available sessions: {list(sessions.keys())}")

    # Check if login is enabled
    import os
    login_enabled = os.getenv("LOGIN_ENABLE", "true").lower() == "true"
    logger.info(f"Login enabled: {login_enabled}")

    # Get user session from token
    user_session = None

    if not login_enabled:
        # If login is disabled, create a unique session for each connection
        # Use the WebSocket connection's client info to create a unique identifier
        import secrets
        client_host = websocket.client.host if websocket.client else "unknown"
        client_port = websocket.client.port if websocket.client else 0
        unique_id = f"{client_host}_{client_port}_{secrets.token_hex(8)}"
        username = f"user_{unique_id}"

        logger.info(f"Login disabled, creating unique session for {username}")
        # Always create a new session for each WebSocket connection when login is disabled
        user_session = session_manager.create_session(
            username=username,
            aws_login="",
            customer_name=""
        )
        logger.info(f"Created unique user session: {user_session.session_id}")
    elif session_token and session_token in sessions:
        user_data = sessions[session_token]
        logger.info(f"Found user data for session_token: {user_data}")
        # Find or create user session
        user_session = session_manager.get_session_by_username(user_data["username"])
        if not user_session:
            user_session = session_manager.create_session(
                username=user_data["username"],
                aws_login=user_data.get("aws_login", ""),
                customer_name=user_data.get("customer_name", "")
            )
            logger.info(f"Created new user session: {user_session.session_id}")
        else:
            logger.info(f"Found existing user session: {user_session.session_id}")
    else:
        logger.warning(f"No valid session found. session_token: {session_token}, sessions available: {bool(sessions)}")

    if not user_session:
        # If no valid session, close the connection
        logger.error("Closing WebSocket connection due to invalid session")
        await websocket.close(code=1008, reason="Invalid session")
        return

    # Connect to session-aware connection manager
    await user_connection_manager.connect(websocket, user_session.session_id)

    # Clear session-specific log buffers when a new connection is established
    user_session.log_buffer = []

    # Send initial connection message
    timestamp = datetime.now().strftime("%H:%M:%S")
    await user_connection_manager.send_json_to_user(user_session.session_id, {
        "type": "info",
        "timestamp": timestamp,
        "data": f"Connected to server. Session: {user_session.session_id}"
    })

    try:
        while True:
            # Wait for messages from the client
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                if message.get('action') == 'clear_logs':
                    # Clear the session-specific log buffer
                    user_session.log_buffer = []
                    await user_connection_manager.send_json_to_user(user_session.session_id, {
                        "type": "info",
                        "timestamp": datetime.now().strftime("%H:%M:%S"),
                        "data": "Logs cleared by client request"
                    })
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received from session {user_session.session_id}: {data}")
    except WebSocketDisconnect:
        user_connection_manager.disconnect(websocket)

# Start desktop instance (session-aware)
async def start_desktop(session_id: str):
    user_session = session_manager.get_session(session_id)
    if not user_session:
        error_msg = f"Invalid session ID: {session_id}"
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": error_msg})
        return {"status": "error", "message": error_msg}

    await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Starting desktop stream..."})

    try:
        logger.info(f"Creating sandbox for session {session_id}...")

        # Check if session already has a sandbox
        if user_session.sandbox_instance:
            logger.info(f"Session {session_id} already has a sandbox, reusing it")
            desktop = user_session.sandbox_instance
            stream_url = user_session.stream_url
        else:
            # Create new sandbox for this session
            try:
                api_key = os.environ.get("API_KEY")
                template = os.environ.get("TEMPLATE")
                domain = os.environ.get("DOMAIN")
                timeout = int(os.environ.get("TIMEOUT", 1200))

                logger.info(f"Using template: {template}, domain: {domain}, timeout: {timeout} for session {session_id}")
                logger.info(f"API_KEY present: {bool(api_key)}")

                # Create sandbox with detailed error handling
                logger.info(f"Initializing Sandbox object for session {session_id}...")
                desktop = Sandbox(
                        api_key=api_key,
                        template=template,
                        domain=domain,
                        timeout=timeout,
                        metadata={
                            "purpose": "sandbox-on-aws",
                            "session_id": session_id
                        }
                    )
                logger.info(f"Sandbox object initialized for session {session_id}, sandbox_id: {desktop.sandbox_id}")

                # Log sandbox object details
                for attr in ['sandbox_id', 'status', 'ready']:
                    if hasattr(desktop, attr):
                        logger.info(f"Session {session_id} Sandbox {attr}: {getattr(desktop, attr)}")
                    else:
                        logger.info(f"Session {session_id} Sandbox has no attribute '{attr}'")

                # Log stream object details
                if hasattr(desktop, 'stream'):
                    logger.info(f"Session {session_id} Stream object exists, checking properties...")
                    stream_obj = desktop.stream
                    for attr in ['id', 'status']:
                        if hasattr(stream_obj, attr):
                            logger.info(f"Session {session_id} Stream {attr}: {getattr(stream_obj, attr)}")
                else:
                    logger.error(f"Session {session_id} Sandbox has no 'stream' attribute!")
                    raise Exception("Sandbox missing stream attribute")
            except Exception as e:
                logger.error(f"Error creating sandbox for session {session_id}: {e}", exc_info=True)
                await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": f"Error creating sandbox: {str(e)}"})
                return {"status": "error", "message": f"Error creating sandbox: {str(e)}"}

            # Get auth key and stream URL
            try:
                logger.info(f"Starting stream for session {session_id}...")
                desktop.stream.start(require_auth=True)

                logger.info(f"Getting stream auth key for session {session_id}...")
                auth_key = desktop.stream.get_auth_key()
                logger.info(f"Auth key retrieved successfully for session {session_id}")

                logger.info(f"Getting stream URL for session {session_id}...")
                stream_url = desktop.stream.get_url(auth_key=auth_key)
                logger.info(f"Stream URL generated for session {session_id}: {stream_url}")

                # Store in user session
                user_session.sandbox_instance = desktop
                user_session.stream_url = stream_url

            except Exception as e:
                logger.error(f"Error getting stream URL for session {session_id}: {e}", exc_info=True)
                await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": f"Error getting stream URL: {str(e)}"})
                return {"status": "error", "message": f"Error getting stream URL: {str(e)}"}

        # Get timeout from environment variables or use default
        timeout = int(os.environ.get("TIMEOUT", 1200))
        logger.info(f"Using timeout value: {timeout} for session {session_id}")

        # Send success message to client
        logger.info(f"Sending desktop_started event to session {session_id}")
        await user_connection_manager.send_json_to_user(session_id, {
            "type": "desktop_started",
            "data": {
                "sandbox_id": desktop.sandbox_id,
                "stream_url": stream_url,
                "timeout": timeout
            }
        })
        logger.info(f"Desktop started event sent successfully to session {session_id}")
    except Exception as e:
        logger.error(f"Unexpected error in start_desktop for session {session_id}: {e}", exc_info=True)
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": f"Unexpected error: {str(e)}"})
        return {"status": "error", "message": f"Unexpected error: {str(e)}"}

    return {"status": "success", "stream_url": stream_url}

# Setup environment (session-aware)
async def setup_env(session_id: str, background_tasks: BackgroundTasks = None):
    user_session = session_manager.get_session(session_id)
    if not user_session or not user_session.sandbox_instance:
        error_msg = f"No desktop instance available for session {session_id}"
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": error_msg})
        return {"status": "error", "message": error_msg}

    await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Setting up environment in background..."})

    # Add to background tasks
    if background_tasks:
        background_tasks.add_task(setup_env_in_background, session_id)
    else:
        # If not called with background_tasks (e.g. direct API call)
        # create a new background task manually
        asyncio.create_task(setup_env_in_background(session_id))

    return {"status": "success", "message": "Environment setup started in background"}

# Setup environment in background (session-aware)
async def setup_env_in_background(session_id: str):
    """Run the environment setup in background so it can be cancelled"""
    user_session = session_manager.get_session(session_id)
    if not user_session or not user_session.sandbox_instance:
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": "No desktop instance available"})
        return

    desktop = user_session.sandbox_instance

    try:
        # Step 1: Copy files to sandbox
        await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Copying files to sandbox..."})

        try:
            with open('bedrock.py', 'r') as f1:
                _code = f1.read()
                desktop.files.write('/tmp/bedrock.py', _code)
                await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Copied bedrock.py to /tmp/bedrock.py"})

        except Exception as e:
            logger.error(f"Error copying files for session {session_id}: {e}")
            await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": f"Error copying files: {str(e)}"})
            return

        # Step 2: Create STS credentials
        await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Creating AWS credentials..."})
        credentials = create_sts()
        if not credentials:
            await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": "Failed to create AWS credentials"})
            return

        creds_content = f"""[default]
    aws_access_key_id={credentials['AccessKeyId']}
    aws_secret_access_key={credentials['SecretAccessKey']}
    aws_session_token={credentials['SessionToken']}
    """
        desktop.files.write('~/.aws/credentials', creds_content)
        await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "AWS credentials created successfully"})

        await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Installing Playwright browser..."})

        stdout_logger = WebSocketLogger(session_id, "stdout")
        stderr_logger = WebSocketLogger(session_id, "stderr")

        cmd = 'playwright install chromium --with-deps --no-shell'
        logger.info(f"Running command in background for session {session_id}: {cmd}")
        current_command = desktop.commands.run(
            cmd,
            on_stdout=stdout_logger,
            on_stderr=stderr_logger,
            background=True
        )

        # Store the command in the user session
        user_session.current_command = current_command
        logger.info(f"Command started for session {session_id} with id: {getattr(current_command, 'id', 'unknown')}")

        # Wait for command to complete
        result = await asyncio.to_thread(current_command.wait)
        # CommandResult object doesn't have a get method, access exit_code directly
        if hasattr(result, 'exit_code') and result.exit_code != 0:
            await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": "Failed to install Playwright browser"})
            return

        await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Environment setup completed successfully"})


    except Exception as e:
        logger.error(f"Error setting up environment for session {session_id}: {e}")
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": f"Error setting up environment: {str(e)}"})
    finally:
        # Clear the command from the user session
        user_session = session_manager.get_session(session_id)
        if user_session:
            user_session.current_command = None

# Run task (session-aware)
async def run_task(session_id: str, query: str = Form(...), background_tasks: BackgroundTasks = None):
    user_session = session_manager.get_session(session_id)
    if not user_session or not user_session.sandbox_instance:
        error_msg = f"No desktop instance available for session {session_id}"
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": error_msg})
        return {"status": "error", "message": error_msg}

    await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": f"Running task: {query}"})

    # Add to background tasks
    if background_tasks:
        background_tasks.add_task(run_task_in_background, session_id, query)
    else:
        # If not called with background_tasks (e.g. direct API call)
        # create a new background task manually
        asyncio.create_task(run_task_in_background(session_id, query))

    return {"status": "success", "message": "Task started in background"}

# Run task in background (session-aware)
async def run_task_in_background(session_id: str, query: str):
    """Run the task in background so it can be cancelled"""
    user_session = session_manager.get_session(session_id)
    if not user_session or not user_session.sandbox_instance:
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": "No desktop instance available"})
        return

    desktop = user_session.sandbox_instance

    try:
        # Run bedrock.py with the query
        stdout_logger = WebSocketLogger(session_id, "stdout")
        stderr_logger = WebSocketLogger(session_id, "stderr")

        # Clear any existing command reference
        user_session.current_command = None

        # Start the command in BACKGROUND mode with the proper E2B API
        # This returns immediately but keeps the process running
        logger.info(f"Starting task in background mode for session {session_id}")
        cmd = f"python3.11 /tmp/bedrock.py --query '{query}'"
        logger.info(f"Running command in background for session {session_id}: {cmd}")
        current_command = desktop.commands.run(
            cmd,
            on_stdout=stdout_logger,
            on_stderr=stderr_logger,
            background=True,  # This is key to allow immediate kill
            timeout=0  # Disable timeout to prevent 'context deadline exceeded' errors
        )

        # Store command in user session
        user_session.current_command = current_command

        # Log more detailed information about the command object
        logger.info(f"Command started for session {session_id} with id: {getattr(current_command, 'id', 'unknown')}")
        logger.info(f"Command object for session {session_id}: {current_command}")
        # Log available attributes
        for attr in ['id', 'sandbox_id', 'process_id', 'exit_code', 'status']:
            if hasattr(current_command, attr):
                logger.info(f"Session {session_id} Command {attr}: {getattr(current_command, attr)}")

        # Wait for the command to complete naturally or be killed externally
        result = await asyncio.to_thread(current_command.wait)

        # Check the exit code to see if it completed successfully or was killed
        exit_code = getattr(result, 'exit_code', None)
        if exit_code == 0:
            await user_connection_manager.send_json_to_user(session_id, {"type": "task_completed", "data": "Task completed successfully"})
        elif exit_code is None or exit_code < 0:
            # Likely killed
            await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Task was terminated"})
        else:
            await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": f"Task failed with exit code: {exit_code}"})

    except Exception as e:
        logger.error(f"Error running task for session {session_id}: {e}")
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": f"Error running task: {str(e)}"})
    finally:
        # Clear the command from the user session
        user_session = session_manager.get_session(session_id)
        if user_session:
            user_session.current_command = None

# Kill desktop (session-aware)
async def kill_desktop(session_id: str):
    user_session = session_manager.get_session(session_id)
    if not user_session or not user_session.sandbox_instance:
        error_msg = f"No desktop instance available for session {session_id}"
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": error_msg})
        return {"status": "error", "message": error_msg}

    desktop = user_session.sandbox_instance
    current_command = user_session.current_command

    await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Killing desktop instance and workflow processes..."})

    try:
        # First, kill any running command with the proper API
        if current_command:
            logger.info(f"Killing current command for session {session_id}: {current_command}")
            for attr in ['id', 'sandbox_id', 'process_id', 'exit_code', 'status']:
                if hasattr(current_command, attr):
                    logger.info(f"Session {session_id} Command {attr} before kill: {getattr(current_command, attr)}")

            try:
                # Use the E2B command kill method
                current_command.kill()
                logger.info(f"Command kill() method called successfully for session {session_id}")

                # Log command attributes after killing
                for attr in ['id', 'sandbox_id', 'process_id', 'exit_code', 'status']:
                    if hasattr(current_command, attr):
                        logger.info(f"Session {session_id} Command {attr} after kill: {getattr(current_command, attr)}")
            except Exception as e:
                logger.error(f"Error killing command for session {session_id}: {e}")

        # Force kill any Python processes for good measure
        try:
            await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Killing all Python processes"})
            cmd = "pkill -9 python"
            logger.info(f"Running command in background for session {session_id}: {cmd}")
            result = desktop.commands.run(cmd, timeout=2)
            logger.info(f"Command started for session {session_id} with id: {getattr(result, 'id', 'unknown')}")
            for attr in ['id', 'sandbox_id', 'process_id', 'exit_code', 'status']:
                if hasattr(result, attr):
                    logger.info(f"Session {session_id} Command {attr}: {getattr(result, attr)}")
            await user_connection_manager.send_json_to_user(session_id, {"type": "info", "data": "Killed all Python processes"})
        except Exception as process_error:
            # Log but continue with sandbox kill
            logger.warning(f"Error killing processes for session {session_id}: {process_error}")

        # Now kill the desktop sandbox
        desktop.kill()

        # Clear session resources
        user_session.sandbox_instance = None
        user_session.stream_url = None
        user_session.current_command = None

        await user_connection_manager.send_json_to_user(session_id, {"type": "desktop_killed", "data": "Desktop instance killed"})
        return {"status": "success"}
    except Exception as e:
        logger.error(f"Error killing desktop for session {session_id}: {e}")
        await user_connection_manager.send_json_to_user(session_id, {"type": "error", "data": f"Error killing desktop: {str(e)}"})
        return {"status": "error", "message": str(e)}

# Run the entire workflow (session-aware)
async def run_workflow(session_id: str, query: str = Form(...), background_tasks: BackgroundTasks = BackgroundTasks()):
    # Start desktop
    start_result = await start_desktop(session_id)
    if start_result["status"] == "error":
        return start_result

    # Setup environment in background
    setup_result = await setup_env(session_id, background_tasks=background_tasks)
    if setup_result["status"] == "error":
        return setup_result

    # Run task in background
    await run_task(session_id, query, background_tasks=background_tasks)

    return {"status": "success", "message": "Workflow started"}

# Function to initialize shared variables from app.py
def init_shared_vars(app_manager, app_logger, app_ws_handler, app_stdout_capture, app_stderr_capture, app_sessions):
    global manager, logger, ws_handler, stdout_capture, stderr_capture, sessions
    manager = app_manager
    logger = app_logger
    ws_handler = app_ws_handler
    stdout_capture = app_stdout_capture
    stderr_capture = app_stderr_capture
    sessions = app_sessions
