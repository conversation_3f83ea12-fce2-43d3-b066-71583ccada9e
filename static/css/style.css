body {
    font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    padding: 20px;
}

.header {
    margin-bottom: 20px;
}

.header h1 {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.main-content {
    height: calc(100vh - 100px);
}

.stream-panel, .control-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 12px 20px;
}

.card-header h3 {
    font-size: 18px;
    margin: 0;
    color: #333;
}

.card-body {
    padding: 20px;
    flex: 1;
    overflow: auto;
    width: 100%;
    display: flex;
    flex-direction: column;
}

#stream-container {
    height: 100%;
    width: 100%;
    background-color: #eee;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

#stream-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #666;
    text-align: center;
    padding: 20px;
}

#stream-frame {
    width: 100%;
    height: 100%;
    border: none;
}

#log-container {
    flex: 1;
    min-height: 400px; /* Reduced height since it's now inside the controls card */
    overflow-y: auto;
    background-color: #212529;
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
    color: #f8f9fa;
    width: 100%; /* Ensure full width */
    text-align: left !important;
}

/* Ensure the logs div inside log-container is also left-aligned */
#logs {
    text-align: left !important;
    width: 100%;
    display: block;
}

#logs .log-entry {
    margin-bottom: 5px;
    word-break: break-word;
    text-align: left !important;
    line-height: 1.4;
    display: block !important;
    white-space: pre-wrap;
    padding: 0 !important;
    margin-left: 0 !important;
    text-indent: 0 !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
}

.log-timestamp {
    color: #adb5bd;
    margin-right: 8px;
}

.log-stdout {
    color: #8bc34a;
}

.log-stderr {
    color: #ff5252;
}

.log-info {
    color: #2196f3;
}

.log-error {
    color: #ff5252;
}

.log-success {
    color: #4caf50;
}

/* Ensure badges in log entries are properly aligned */
#logs .log-entry .badge {
    margin-right: 8px !important;
    margin-left: 0 !important;
    vertical-align: baseline !important;
    display: inline !important;
}

/* Ensure timestamps are consistently formatted */
#logs .log-entry .text-muted {
    margin-right: 8px !important;
    margin-left: 0 !important;
    display: inline !important;
}

/* Override any Bootstrap flexbox or alignment on log entry children */
#logs .log-entry * {
    text-align: left !important;
}

.control-panel .card {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.logs-section {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
    display: block !important;
    text-align: left !important;
    align-items: flex-start !important;
}

/* Modern Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 18px;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Primary button (Run Task) */
.btn-info {
    background: linear-gradient(135deg, #2196f3, #0d6efd);
    color: white;
}

/* Danger button (Stop Sandbox) */
.btn-danger {
    background: linear-gradient(135deg, #ff5252, #dc3545);
    color: white;
}

/* Secondary buttons */
.btn-outline-secondary {
    border: 1px solid #ced4da;
    background: white;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #f8f9fa;
    color: #495057;
}

.btn-outline-secondary.active {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    border-color: transparent;
}

/* Badge styles */
.badge {
    font-weight: 500;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@media (max-width: 767.98px) {
    .main-content {
        height: auto;
    }
    
    .stream-panel, .control-panel {
        height: auto;
        margin-bottom: 20px;
    }
    
    #stream-container {
        height: 400px;
    }
    
    #log-container {
        height: 500px; /* Smaller height on mobile but still larger than original */
    }
}
